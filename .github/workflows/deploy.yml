name: Deploy to Vercel

on:
  push:
    branches:
      - main # or your production branch

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4 # Use a recent version
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25.2.0 # Use a recent version
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          # Optional: Add other parameters like `vercel-args`, `build-env`, etc.
          # For example, to deploy to a specific environment:
          # vercel-args: '--prod' # for production deployments
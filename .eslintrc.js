module.exports = {
  extends: [
    'next/core-web-vitals',
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended'
  ],
  parser: '@typescript-eslint/parser',
  plugins: [ '@typescript-eslint' ],
  root: true,
  rules: {
    'no-unused-vars': 'error',
    'no-console': [ 'error', { allow: [ 'error' ] } ],
    'no-debugger': 'error',
    '@typescript-eslint/no-explicit-any': 'warn',
    'quotes': [ 'error', 'single' ],
    'jsx-quotes': [ 'error', 'prefer-single' ],
    'semi': [ 'error', 'always' ],
    'indent': [ 'error', 2 ],
    'object-curly-spacing': [ 'error', 'always' ],
    'array-bracket-spacing': [ 'error', 'always' ],
    'comma-spacing': [ 'error', { 'before': false, 'after': true } ],
    'key-spacing': [ 'error', { 'beforeColon': false, 'afterColon': true } ],
    'space-infix-ops': 'error',
    'space-before-blocks': 'error',
    'keyword-spacing': [ 'error', { 'before': true, 'after': true } ],
    'space-before-function-paren': [ 'error', {
      'anonymous': 'always',
      'named': 'never',
      'asyncArrow': 'always'
    } ],
    'no-multiple-empty-lines': [ 'error', { 'max': 1, 'maxEOF': 0 } ],
    'padding-line-between-statements': [
      'error',
      { 'blankLine': 'always', 'prev': '*', 'next': 'return' }
    ]
  }
};
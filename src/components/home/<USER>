import * as React from 'react';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/card';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel';

const slides = [
  {
    src: '/images/1.jpg',
    title: 'Services',
    subtitle: 'Weddings',
  },
  {
    src: '/images/2.jpg',
    title: 'Services',
    subtitle: 'Tablescapes',
  },
  {
    src: '/images/3.jpg',
    title: 'Events',
    subtitle: 'Corporate Events',
  },
  {
    src: '/images/4.jpg',
    title: 'Events',
    subtitle: 'Corporate Events',
  },
  {
    src: '/images/5.jpg',
    title: 'Events',
    subtitle: 'Corporate Events',
  },
  {
    src: '/images/6.jpg',
    title: 'Events',
    subtitle: 'Corporate Events',
  },
];

export function Banner() {
  return (
    <div className='w-full pt-8 pb-8 flex justify-center bg-[#F4EFE9]'>
      <div className='w-full max-w-6xl px-4'>
        <Carousel
          opts={{ align: 'start', loop: true }}
          className='w-full'
        >
          <CarouselContent>
            {slides.map((slide, index) => (
              <CarouselItem key={index} className='basis-full sm:basis-1/2 lg:basis-1/3'>
                <div className='px-2'>
                  <Card className='overflow-hidden border-0 shadow-none bg-transparent'>
                    <CardContent className='relative aspect-[3/4] p-0 group'>
                      <Image
                        src={slide.src}
                        alt={slide.subtitle}
                        fill
                        className='object-cover'
                      />
                      <div className='absolute inset-0 bg-white/60 backdrop-blur-sm m-4 sm:m-8 flex items-center justify-center opacity-0 transition-opacity duration-300 group-hover:opacity-100 group-focus-within:opacity-100'>
                        <div className='text-center px-2'>
                          <div className='text-xs sm:text-sm tracking-[0.3em] uppercase mb-2'>{slide.title}</div>
                          <div className='text-xl sm:text-3xl md:text-4xl font-serif'>{slide.subtitle}</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className='-left-2 sm:-left-8 bg-white/80 border-0 shadow hover:bg-red-50' />
          <CarouselNext className='-right-2 sm:-right-8 bg-white/80 border-0 shadow hover:bg-red-50' />
        </Carousel>
      </div>
    </div>
  );
}
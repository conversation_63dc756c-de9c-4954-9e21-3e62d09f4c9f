import React from 'react';

export const Invitation: React.FC = () => {
  return (
    <section className='py-20 bg-gradient-to-br from-stone-50 via-amber-50 to-orange-50'>
      <div className='max-w-2xl mx-auto px-6'>
        {/* Save the Date Card - Exact Design Match */}
        <div className='bg-white rounded-2xl shadow-2xl p-8 lg:p-12 relative overflow-hidden'>
          {/* Decorative border */}
          <div className='absolute inset-0 border-4 border-amber-200 rounded-2xl'></div>
          <div className='absolute inset-2 border-2 border-amber-300 rounded-xl'></div>
          
          {/* Corner decorations */}
          <div className='absolute top-3 left-3 w-6 h-6 border-l-2 border-t-2 border-amber-400 rounded-tl-lg'></div>
          <div className='absolute top-3 right-3 w-6 h-6 border-r-2 border-t-2 border-amber-400 rounded-tr-lg'></div>
          <div className='absolute bottom-3 left-3 w-6 h-6 border-l-2 border-b-2 border-amber-400 rounded-bl-lg'></div>
          <div className='absolute bottom-3 right-3 w-6 h-6 border-r-2 border-b-2 border-amber-400 rounded-br-lg'></div>

          <div className='text-center relative z-10'>
            {/* Save the Date Header */}
            <div className='mb-8'>
              <h1 className='text-2xl sm:text-3xl font-elegant text-amber-800 mb-4 tracking-widest'>
                SAVE THE DATE
              </h1>
              <div className='w-16 h-0.5 bg-amber-500 mx-auto mb-4'></div>
            </div>

            {/* Couple names */}
            <div className='mb-8'>
              <h2 className='text-4xl sm:text-5xl font-cursive text-amber-900 mb-2'>PHƯƠNG NGA</h2>
              <div className='w-20 h-0.5 bg-amber-400 mx-auto mb-4'></div>
              <p className='text-sm font-traditional text-amber-700 tracking-widest uppercase'>Are getting married</p>
            </div>

            {/* Wedding details */}
            <div className='space-y-4 mb-8 text-amber-800'>
              <div className='my-6'>
                <p className='text-xl sm:text-2xl font-elegant text-amber-900 mb-1'>Saturday, September 21st, 2025</p>
                <p className='text-sm font-traditional text-amber-700'>at five thirty in the afternoon</p>
              </div>
              <div className='space-y-1'>
                <p className='text-sm font-traditional text-amber-800'>MAPLE HOTEL & APARTMENT</p>
                <p className='text-xs font-traditional text-amber-700'>16 Tôn Đản, Lộc Thọ, Nha Trang, Khánh Hòa</p>
              </div>
            </div>

            <div className='w-16 h-0.5 bg-amber-500 mx-auto mb-6'></div>
            
            <p className='text-sm font-script text-amber-800'>Formal invitation to follow</p>
          </div>
        </div>
      </div>
    </section>
  );
};
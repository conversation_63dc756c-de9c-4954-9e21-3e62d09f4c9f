import React from 'react';
import Image from 'next/image';

export const Venue: React.FC = () => {
  return (
    <section className='py-20 bg-gradient-to-br from-stone-50 via-amber-50 to-orange-50'>
      <div className='max-w-6xl mx-auto px-6'>
        {/* Section Header */}
        <div className='text-center mb-16'>
          <h2 className='text-4xl sm:text-5xl lg:text-6xl font-elegant text-amber-800 mb-4'>DRESS CODE</h2>
          <div className='w-24 h-1 bg-amber-500 mx-auto mb-6'></div>
        </div>

        {/* Dress Code Section - Exact Design Match */}
        <div className='relative bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl p-8 lg:p-12 mb-16'>
          {/* Decorative border */}
          <div className='absolute inset-0 border-4 border-amber-200 rounded-2xl'></div>
          <div className='absolute inset-2 border-2 border-amber-300 rounded-xl'></div>
          
          <div className='text-center relative z-10'>
            <h3 className='text-2xl font-elegant text-amber-800 mb-8'>DRESS CODE</h3>
            
            {/* Color swatches */}
            <div className='flex justify-center gap-6 mb-8'>
              <div className='w-12 h-12 border-2 border-amber-300 rounded-full bg-white'></div>
              <div className='w-12 h-12 border-2 border-amber-300 rounded-full bg-amber-600'></div>
              <div className='w-12 h-12 border-2 border-amber-300 rounded-full bg-gray-800'></div>
            </div>
            
            <p className='text-amber-700 font-traditional max-w-2xl mx-auto'>
              Please dress in elegant attire. We suggest colors that complement our wedding theme: 
              cream, gold, and black tones.
            </p>
          </div>
        </div>

        {/* Timeline Section */}
        <div className='relative bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl p-8 lg:p-12'>
          {/* Decorative border */}
          <div className='absolute inset-0 border-4 border-amber-200 rounded-2xl'></div>
          <div className='absolute inset-2 border-2 border-amber-300 rounded-xl'></div>
          
          <div className='text-center relative z-10'>
            <h3 className='text-2xl font-elegant text-amber-800 mb-8'>TIMELINE</h3>
            
            {/* Timeline with icons */}
            <div className='space-y-6'>
              <div className='flex items-center justify-center gap-4'>
                <div className='w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center'>
                  <span className='text-amber-600 text-sm'>📷</span>
                </div>
                <div className='text-left'>
                  <p className='text-lg font-elegant text-amber-800'>17:30</p>
                  <p className='text-sm font-traditional text-amber-700'>ĐÓN TIẾP KHÁCH MỜI</p>
                </div>
              </div>
              
              <div className='flex items-center justify-center gap-4'>
                <div className='w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center'>
                  <span className='text-amber-600 text-sm'>💍</span>
                </div>
                <div className='text-left'>
                  <p className='text-lg font-elegant text-amber-800'>18:00</p>
                  <p className='text-sm font-traditional text-amber-700'>BẮT ĐẦU LỄ THÀNH HÔN</p>
                </div>
              </div>
              
              <div className='flex items-center justify-center gap-4'>
                <div className='w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center'>
                  <span className='text-amber-600 text-sm'>🍽️</span>
                </div>
                <div className='text-left'>
                  <p className='text-lg font-elegant text-amber-800'>18:30</p>
                  <p className='text-sm font-traditional text-amber-700'>CHUNG VUI KHAI TIỆC</p>
                </div>
              </div>
              
              <div className='flex items-center justify-center gap-4'>
                <div className='w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center'>
                  <span className='text-amber-600 text-sm'>💃</span>
                </div>
                <div className='text-left'>
                  <p className='text-lg font-elegant text-amber-800'>20:00</p>
                  <p className='text-sm font-traditional text-amber-700'>MINIGAME VÀ KHIÊU VŨ</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
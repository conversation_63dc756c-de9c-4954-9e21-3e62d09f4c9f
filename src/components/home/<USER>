import React from 'react';
import Image from 'next/image';

export const OurStory: React.FC = () => {
  return (
    <section className='py-20 bg-gradient-to-br from-stone-50 via-amber-50 to-orange-50'>
      <div className='max-w-6xl mx-auto px-6'>
        {/* Section Header */}
        <div className='text-center mb-16'>
          <h2 className='text-4xl sm:text-5xl lg:text-6xl font-elegant text-amber-800 mb-4'>THE STORY of LOVE</h2>
          <div className='w-24 h-1 bg-amber-500 mx-auto mb-6'></div>
        </div>

        {/* Couple Introduction */}
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16'>
          {/* Bride */}
          <div className='text-center'>
            <div className='relative w-48 h-48 mx-auto mb-6 rounded-full overflow-hidden shadow-lg'>
              <div className='w-full h-full bg-gradient-to-br from-amber-100 to-amber-200 flex items-center justify-center'>
                <span className='text-4xl font-elegant text-amber-600'>PN</span>
              </div>
            </div>
            <div className='text-amber-600 text-sm font-traditional tracking-widest uppercase mb-2'>Cô dâu</div>
            <h3 className='text-2xl font-elegant text-amber-800 mb-2'>PHƯƠNG NGA</h3>
            <p className='text-amber-700 font-traditional'>09.11.1999</p>
          </div>

          {/* Groom */}
          <div className='text-center'>
            <div className='relative w-48 h-48 mx-auto mb-6 rounded-full overflow-hidden shadow-lg'>
              <div className='w-full h-full bg-gradient-to-br from-amber-100 to-amber-200 flex items-center justify-center'>
                <span className='text-4xl font-elegant text-amber-600'>BA</span>
              </div>
            </div>
            <div className='text-amber-600 text-sm font-traditional tracking-widest uppercase mb-2'>Chú rể</div>
            <h3 className='text-2xl font-elegant text-amber-800 mb-2'>BÌNH AN</h3>
            <p className='text-amber-700 font-traditional'>24.07.1994</p>
          </div>
        </div>

        {/* Timeline */}
        <div className='relative'>
          <div className='absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-amber-200 hidden lg:block'></div>
          
          <div className='space-y-16'>
            {/* 2022 - Fateful Meeting */}
            <div className='flex flex-col lg:flex-row items-center gap-8 lg:gap-16'>
              <div className='lg:w-1/2 lg:text-right'>
                <div className='bg-white/90 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-amber-100'>
                  <div className='text-amber-600 text-sm font-traditional tracking-widest uppercase mb-2'>2022 – GẶP GỠ ĐỊNH MỆNH</div>
                  <p className='text-amber-700 font-traditional leading-relaxed'>
                    Phương Nga - một cô gái xinh đẹp và tài năng, đã gặp Bình An - một chàng trai trẻ đầy triển vọng trong ngành giải trí. 
                    Từ cái nhìn đầu tiên, họ đã có ấn tượng đặc biệt với nhau. Những cuộc trò chuyện dài sau đó đã giúp họ nhận ra 
                    mối liên kết tâm hồn sâu sắc giữa hai người.
                  </p>
                </div>
              </div>
              
              <div className='lg:w-1/2'>
                <div className='relative w-48 h-48 mx-auto lg:mx-0 rounded-xl overflow-hidden shadow-lg'>
                  <div className='w-full h-full bg-gradient-to-br from-amber-100 to-amber-200 flex items-center justify-center'>
                    <span className='text-2xl font-elegant text-amber-600'>2022</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 2023 - Challenges and Bonding */}
            <div className='flex flex-col lg:flex-row-reverse items-center gap-8 lg:gap-16'>
              <div className='lg:w-1/2 lg:text-left'>
                <div className='bg-white/90 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-amber-100'>
                  <div className='text-amber-600 text-sm font-traditional tracking-widest uppercase mb-2'>2023 – THỬ THÁCH VÀ GẮN KẾT</div>
                  <p className='text-amber-700 font-traditional leading-relaxed'>
                    Với sự nghiệp bận rộn trong showbiz, họ thường xuyên phải xa nhau. Dư luận và tin đồn đôi khi gây khó khăn, 
                    nhưng sự tin tưởng và thấu hiểu đã giúp họ vượt qua mọi thử thách. Trong suốt hai năm, Bình An âm thầm ủng hộ 
                    Phương Nga trong các sự kiện lớn, còn cô cũng lặng lẽ theo dõi sự tiến bộ của anh. Cuối năm 2024, Bình An đã 
                    bất ngờ cầu hôn Phương Nga trong một khung cảnh lãng mạn, và cô đã xúc động chấp nhận.
                  </p>
                </div>
              </div>
              
              <div className='lg:w-1/2'>
                <div className='relative w-48 h-48 mx-auto lg:mx-0 rounded-xl overflow-hidden shadow-lg'>
                  <div className='w-full h-full bg-gradient-to-br from-amber-100 to-amber-200 flex items-center justify-center'>
                    <span className='text-2xl font-elegant text-amber-600'>2023</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
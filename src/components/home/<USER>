import React from 'react';

type WeddingStop = {
  id: number;
  country: string;
  cityScript: string;
  monthYear: string;
  time: string;
  description: string;
};

const STOPS: WeddingStop[] = [
  { 
    id: 1, 
    country: '<PERSON><PERSON> đón khách', 
    cityScript: '17:30', 
    monthYear: '<PERSON><PERSON> tiếp khách mời', 
    time: '17:30',
    description: 'Chào đón và đón tiếp khách mời'
  },
  { 
    id: 2, 
    country: '<PERSON><PERSON> thành hôn', 
    cityScript: '18:00', 
    monthYear: 'Bắt đầu lễ cưới', 
    time: '18:00',
    description: '<PERSON><PERSON> lễ cưới chính thức'
  },
  { 
    id: 3, 
    country: 'Tiệc cưới', 
    cityScript: '18:30', 
    monthYear: 'Chung vui khai tiệc', 
    time: '18:30',
    description: 'Bữa tiệc cưới và chúc mừng'
  },
  { 
    id: 4, 
    country: '<PERSON>hi<PERSON><PERSON> vũ', 
    cityScript: '20:00', 
    monthYear: 'Minigame và khiêu vũ', 
    time: '20:00',
    description: '<PERSON>ạt động vui chơi và khiêu vũ'
  },
];

export const WeddingDates: React.FC = () => {
  return (
    <section id='wedding-dates' className='w-full flex justify-center bg-gradient-to-br from-stone-50 via-amber-50 to-orange-50 py-20'>
      <div className='w-full max-w-4xl px-6 text-center'>
        <h2 className='text-4xl sm:text-5xl lg:text-6xl font-elegant text-amber-800 mb-4'>TIMELINE</h2>
        <div className='w-24 h-1 bg-amber-500 mx-auto mb-6'></div>
        <p className='text-lg font-traditional text-amber-700 max-w-2xl mx-auto mb-12'>Chi tiết các sự kiện trong ngày trọng đại của chúng tôi</p>
        
        {/* Timeline with icons - Exact Design Match */}
        <div className='relative space-y-8'>
          {STOPS.map((stop, index) => (
            <div key={stop.id} className='relative flex flex-col lg:flex-row items-center lg:items-start text-left'>
              {/* Timeline dot and line */}
              <div className='absolute left-1/2 transform -translate-x-1/2 lg:relative lg:left-auto lg:transform-none lg:w-24 flex justify-center'>
                <div className='w-4 h-4 bg-amber-500 rounded-full z-10 border-2 border-white shadow-md'></div>
                {index < STOPS.length - 1 && (
                  <div className='absolute top-4 w-px h-full bg-amber-200 hidden lg:block'></div>
                )}
              </div>
              
              {/* Content card */}
              <div className={`flex-1 lg:w-1/2 ${index % 2 === 0 ? 'lg:pr-12 lg:text-right' : 'lg:pl-12 lg:text-left'}`}>
                <div className='bg-white/90 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-amber-100'>
                  <div className='flex items-center mb-4'>
                    <div className='w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center mr-4'>
                      <span className='text-amber-600 text-sm'>📅</span>
                    </div>
                    <h3 className='text-2xl font-elegant text-amber-800'>{stop.country}</h3>
                  </div>
                  <p className='text-amber-700 font-traditional mb-2'>
                    <span className='font-medium'>{stop.time}</span>
                  </p>
                  <p className='text-amber-600 font-traditional text-sm mb-4'>{stop.monthYear}</p>
                  <p className='text-amber-700 font-traditional leading-relaxed'>{stop.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
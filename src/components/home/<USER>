import Image from 'next/image';
import React from 'react';

export const About: React.FC = () => {
  return (
    <section id='concept-wedding' className='w-full flex justify-center'>
      <div className='w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-10 items-start px-6 lg:px-0 py-16 lg:py-20'>
        <div className='order-2 lg:order-1'>
          <h2 className='text-3xl sm:text-4xl lg:text-5xl font-serif mb-4'>About Phuong Anh</h2>
          <div className='h-[2px] w-16 bg-red-500 mb-6 lg:mb-8' />
          <h3 className='text-lg sm:text-xl lg:text-2xl font-serif mb-4 lg:mb-6'>
            We would love to meet up and chat about how we can make your
            dream wedding happen!
          </h3>
          <div className='space-y-4 lg:space-y-6 text-gray-600 leading-6 lg:leading-8 text-sm sm:text-base'>
            <p>
              Wedding elit miss eget the solin missen ciudino sellus rutrum in
              lacus son nemi vestibulum eleifen ornare drana company tortor
              semper at. Suspendise asin the sedisem tincio the drana numune
              consue.
            </p>
            <p>
              Destination elit miss eget the solin miss ciudino phasellus
              rutrum in lacus miss. Vestibulum eleifen ornare drana sempe
              numune consue.
            </p>
            <p>
              Event elit miss eget the solin miss ciudino phasellus rutrum in
              the lacusi events vestibulum eleifen ornare drana company tortori
              sempe numune consue design elit miss eget the solin misse ciudino
              sellus rutrum in lacus miss vestibulum eleifen ornare drana
              company tortor semper at.
            </p>
          </div>
        </div>
        <div className='about-img order-1 lg:order-2'>
          <div className='img relative w-full aspect-[4/5]'>
            <Image src='/images/about.jpg' alt='about' fill className='object-cover pb-4 pr-4 sm:pb-8 sm:pr-8' />
          </div>
        </div>
      </div>
    </section>
  );
};

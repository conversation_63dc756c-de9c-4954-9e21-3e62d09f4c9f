import React from 'react';
import Image from 'next/image';

export const Hero: React.FC = () => {
  return (
    <section className='relative w-full min-h-screen bg-gradient-to-br from-stone-50 via-amber-50 to-orange-50 flex items-center justify-center py-20'>
      {/* Vietnamese Wedding Invitation Card - Exact Design Match */}
      <div className='relative max-w-2xl mx-auto px-6'>
        <div className='bg-white rounded-2xl shadow-2xl p-8 lg:p-12 relative overflow-hidden'>
          {/* Decorative border with Vietnamese pattern */}
          <div className='absolute inset-0 border-4 border-amber-200 rounded-2xl'></div>
          <div className='absolute inset-2 border-2 border-amber-300 rounded-xl'></div>
          
          {/* Corner decorations - Vietnamese style */}
          <div className='absolute top-3 left-3 w-6 h-6 border-l-2 border-t-2 border-amber-400 rounded-tl-lg'></div>
          <div className='absolute top-3 right-3 w-6 h-6 border-r-2 border-t-2 border-amber-400 rounded-tr-lg'></div>
          <div className='absolute bottom-3 left-3 w-6 h-6 border-l-2 border-b-2 border-amber-400 rounded-bl-lg'></div>
          <div className='absolute bottom-3 right-3 w-6 h-6 border-r-2 border-b-2 border-amber-400 rounded-br-lg'></div>

          <div className='text-center relative z-10'>
            {/* Parents' information - Two columns with divider */}
            <div className='mb-8'>
              <div className='grid grid-cols-2 gap-8 mb-6'>
                <div className='text-left'>
                  <p className='text-sm font-traditional text-amber-800 mb-1'>Ông. Đặng Thái Công</p>
                  <p className='text-sm font-traditional text-amber-800 mb-1'>Bà. Hoàng Mai Hương</p>
                  <p className='text-xs font-traditional text-amber-700'>TP. Nha Trang</p>
                </div>
                <div className='text-right'>
                  <p className='text-sm font-traditional text-amber-800 mb-1'>Ông. Phan Đình Long</p>
                  <p className='text-sm font-traditional text-amber-800 mb-1'>Bà. Nguyễn Thị Mai</p>
                  <p className='text-xs font-traditional text-amber-700'>TP. Đà Nẵng</p>
                </div>
              </div>
              <div className='w-full h-px bg-amber-300 mb-6'></div>
              <p className='text-sm font-traditional text-amber-800 mb-6'>Thân mời đến dự lễ thành hôn của chúng tôi!</p>
            </div>

            {/* Couple's names - Large and prominent */}
            <div className='mb-8'>
              <h2 className='text-4xl sm:text-5xl font-elegant text-amber-900 mb-2'>PHƯƠNG NGA</h2>
              <p className='text-lg font-script text-amber-700 mb-2'>and</p>
              <h2 className='text-4xl sm:text-5xl font-elegant text-amber-900 mb-4'>BÌNH AN</h2>
            </div>

            {/* Event details with horizontal lines */}
            <div className='space-y-4 mb-8 text-amber-800'>
              <div className='w-full h-px bg-amber-300'></div>
              <div className='my-6'>
                <p className='text-lg font-traditional text-amber-900 mb-1'>17:30 - CHỦ NHẬT</p>
                <p className='text-xl sm:text-2xl font-elegant text-amber-900 mb-1'>21.09.2025</p>
              </div>
              <div className='w-full h-px bg-amber-300'></div>
              <div className='space-y-1 mt-6'>
                <p className='text-sm font-traditional text-amber-800'>Địa điểm:</p>
                <p className='text-sm font-traditional text-amber-800 font-medium'>MAPLE HOTEL & APARTMENT</p>
                <p className='text-xs font-traditional text-amber-700'>16 Tôn Đản, Lộc Thọ, Nha Trang, Khánh Hòa</p>
                <div className='flex items-center justify-center mt-2'>
                  <span className='text-xs font-traditional text-amber-700 mr-1'>📍</span>
                  <span className='text-xs font-traditional text-amber-700'>CHỈ ĐƯỜNG</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

import React from 'react';
import Image from 'next/image';

export const Gallery: React.FC = () => {
  return (
    <section className='py-20 bg-gradient-to-br from-stone-50 via-amber-50 to-orange-50'>
      <div className='max-w-6xl mx-auto px-6'>
        {/* Section Header */}
        <div className='text-center mb-16'>
          <h2 className='text-4xl sm:text-5xl lg:text-6xl font-elegant text-amber-800 mb-4'>The ALBUM</h2>
          <div className='w-24 h-1 bg-amber-500 mx-auto mb-6'></div>
          <p className='text-lg font-traditional text-amber-700 max-w-2xl mx-auto'>OF LOVE</p>
        </div>

        {/* Photo Album Layout - Exact Design Match */}
        <div className='relative bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl p-8 lg:p-12'>
          {/* Decorative border */}
          <div className='absolute inset-0 border-4 border-amber-200 rounded-2xl'></div>
          <div className='absolute inset-2 border-2 border-amber-300 rounded-xl'></div>
          
          {/* Film strip effect at top */}
          <div className='flex gap-2 mb-8 overflow-x-auto'>
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className='flex-shrink-0 w-16 h-12 bg-gray-800 rounded-sm relative'>
                <div className='absolute inset-1 bg-gradient-to-br from-amber-100 to-amber-200 rounded-sm flex items-center justify-center'>
                  <span className='text-xs font-elegant text-amber-600'>{i}</span>
                </div>
              </div>
            ))}
          </div>

          {/* Main photo grid */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
            {/* Left photo */}
            <div className='relative group'>
              <div className='relative w-full h-80 rounded-xl overflow-hidden shadow-lg transform rotate-2 hover:rotate-0 transition-transform duration-300'>
                <div className='w-full h-full bg-gradient-to-br from-amber-100 to-amber-200 flex items-center justify-center'>
                  <div className='text-center'>
                    <div className='w-16 h-16 bg-amber-300 rounded-full mx-auto mb-4 flex items-center justify-center'>
                      <span className='text-2xl font-elegant text-amber-700'>💕</span>
                    </div>
                    <p className='text-sm font-traditional text-amber-600'>Wedding Photo</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Right photo */}
            <div className='relative group'>
              <div className='relative w-full h-80 rounded-xl overflow-hidden shadow-lg transform -rotate-2 hover:rotate-0 transition-transform duration-300'>
                <div className='w-full h-full bg-gradient-to-br from-amber-100 to-amber-200 flex items-center justify-center'>
                  <div className='text-center'>
                    <div className='w-16 h-16 bg-amber-300 rounded-full mx-auto mb-4 flex items-center justify-center'>
                      <span className='text-2xl font-elegant text-amber-700'>💕</span>
                    </div>
                    <p className='text-sm font-traditional text-amber-600'>Wedding Photo</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom decorative text */}
          <div className='text-center mt-8'>
            <p className='text-2xl font-elegant text-amber-800'>OF LOVE</p>
          </div>
        </div>
      </div>
    </section>
  );
};
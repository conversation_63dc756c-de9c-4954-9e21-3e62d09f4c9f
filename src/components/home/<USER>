'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Card, CardContent } from '@/components/ui/card';

export const RSVP: React.FC = () => {
  const [ formData, setFormData ] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    attendance: 'yes' as 'yes' | 'no',
    guestCount: '1',
    dietaryNotes: '',
  });
  const [ isSubmitting, setIsSubmitting ] = useState(false);
  const [ submitStatus, setSubmitStatus ] = useState<'idle' | 'success' | 'error'>('idle');
  const [ errorMessage, setErrorMessage ] = useState('');

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');
    setErrorMessage('');

    try {
      const response = await fetch('/api/rsvp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok) {
        setSubmitStatus('success');
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          attendance: 'yes',
          guestCount: '1',
          dietaryNotes: '',
        });
      } else {
        setSubmitStatus('error');
        setErrorMessage(result.error || 'Something went wrong');
      }
    } catch (error) {
      console.error('Network error:', error);
      setSubmitStatus('error');
      setErrorMessage('Network error. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className='w-full flex justify-center bg-gradient-to-br from-stone-50 via-amber-50 to-orange-50 py-20'>
      <div className='w-full max-w-2xl px-6 text-center'>
        {/* RSVP Header */}
        <div className='mb-8'>
          <h2 className='text-4xl sm:text-5xl font-elegant text-amber-800 mb-4'>RSVP</h2>
          <div className='w-24 h-1 bg-amber-500 mx-auto mb-6'></div>
        </div>

        {/* RSVP Message */}
        <div className='mb-8'>
          <p className='text-amber-700 font-traditional text-lg mb-6'>
            Hãy xác nhận sự có mặt của bạn trước ngày 01.07.2025 để chúng mình chuẩn bị đón tiếp một cách chu đáo nhất. Trân trọng!
          </p>
        </div>

        {/* Success/Error Messages */}
        {submitStatus === 'success' && (
          <div className='mb-8 p-6 bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-amber-200'>
            <p className='text-amber-800 font-traditional text-lg mb-4'>
              Cảm ơn bạn đã dành thời gian phản hồi. Chúng mình vô cùng trân quý sự quan tâm của bạn.
            </p>
            <p className='text-2xl font-script text-amber-800'>Thank you!</p>
          </div>
        )}

        {submitStatus === 'error' && (
          <div className='mb-8 p-6 bg-red-100 border border-red-300 rounded-2xl text-red-800'>
            {errorMessage || 'Something went wrong. Please try again.'}
          </div>
        )}

        {/* RSVP Form - Exact Design Match */}
        <div className='relative bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl p-8 lg:p-12'>
          {/* Decorative border */}
          <div className='absolute inset-0 border-4 border-amber-200 rounded-2xl'></div>
          <div className='absolute inset-2 border-2 border-amber-300 rounded-xl'></div>
          
          <form onSubmit={handleSubmit} className='space-y-6 relative z-10'>
            {/* Name Input */}
            <div className='space-y-2'>
              <Label className='text-sm font-traditional text-amber-800'>Trân trọng:</Label>
              <Input 
                placeholder='Jmii' 
                value={formData.firstName}
                onChange={(e) => handleInputChange('firstName', e.target.value)}
                required 
                className='bg-white border-amber-200 focus:ring-amber-500 focus:border-amber-500 text-center'
              />
            </div>

            {/* Message Input */}
            <div className='space-y-2'>
              <Label className='text-sm font-traditional text-amber-800'>Gửi lời nhắn đến cô dâu chú rể</Label>
              <Textarea 
                placeholder='Lời nhắn của bạn...' 
                value={formData.dietaryNotes}
                onChange={(e) => handleInputChange('dietaryNotes', e.target.value)}
                className='bg-white border-amber-200 focus:ring-amber-500 focus:border-amber-500'
                rows={3}
              />
            </div>

            {/* Attendance Dropdown */}
            <div className='space-y-2'>
              <Label className='text-sm font-traditional text-amber-800'>Bạn sẽ đến chứ?</Label>
              <Select value={formData.attendance} onValueChange={(value) => handleInputChange('attendance', value)}>
                <SelectTrigger className='bg-white border-amber-200 focus:ring-amber-500 focus:border-amber-500'>
                  <SelectValue placeholder='Chọn câu trả lời' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='yes'>Có, tôi sẽ đến</SelectItem>
                  <SelectItem value='no'>Không, tôi không thể đến</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Guest Count Dropdown */}
            <div className='space-y-2'>
              <Label className='text-sm font-traditional text-amber-800'>Bạn tham dự cùng ai?</Label>
              <Select value={formData.guestCount} onValueChange={(value) => handleInputChange('guestCount', value)}>
                <SelectTrigger className='bg-white border-amber-200 focus:ring-amber-500 focus:border-amber-500'>
                  <SelectValue placeholder='Chọn số lượng khách' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='1'>Chỉ mình tôi</SelectItem>
                  <SelectItem value='2'>2 người</SelectItem>
                  <SelectItem value='3'>3 người</SelectItem>
                  <SelectItem value='4'>4 người</SelectItem>
                  <SelectItem value='5+'>5+ người</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Guest Type Dropdown */}
            <div className='space-y-2'>
              <Label className='text-sm font-traditional text-amber-800'>Bạn là khách mời của ai?</Label>
              <Select>
                <SelectTrigger className='bg-white border-amber-200 focus:ring-amber-500 focus:border-amber-500'>
                  <SelectValue placeholder='Chọn mối quan hệ' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='bride'>Cô dâu</SelectItem>
                  <SelectItem value='groom'>Chú rể</SelectItem>
                  <SelectItem value='both'>Cả hai</SelectItem>
                  <SelectItem value='family'>Gia đình</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Submit Button */}
            <Button 
              type='submit' 
              disabled={isSubmitting}
              className='w-full bg-amber-500 hover:bg-amber-600 text-white font-traditional font-medium text-lg py-4 rounded-xl disabled:opacity-50'
            >
              {isSubmitting ? 'Đang gửi...' : 'GỬI LỜI NHẮN & XÁC NHẬN'}
            </Button>

            {/* Thank you text */}
            <div className='text-center mt-6'>
              <p className='text-2xl font-script text-amber-800'>Thank you!</p>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
};

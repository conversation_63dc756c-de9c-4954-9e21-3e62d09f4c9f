import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';

export const ContactContent: React.FC = () => {
  return (
    <section className='w-full flex justify-center bg-[#F5F0EC]'>
      <div className='w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-12 px-6 lg:px-0 py-24 items-start'>
        <div>
          <h1 className='text-5xl font-serif mb-4'>Contact</h1>
          <div className='h-[2px] w-16 bg-red-500 mb-8' />
          <p className='text-gray-600 leading-8'>
            We would love to meet up and chat about how we can make your dream
            wedding happen. Send a message and we will get back to you soon.
          </p>
          <div className='mt-8 space-y-3 text-gray-700'>
            <p><span className='font-semibold'>Email:</span> <EMAIL></p>
            <p><span className='font-semibold'>Phone:</span> +****************</p>
            <p><span className='font-semibold'>Location:</span> Ho Chi Minh City, Vietnam</p>
          </div>
        </div>

        <Card className='bg-white shadow-sm border-[#E6E8EC]'>
          <CardContent className='p-6 sm:p-8'>
            <form className='space-y-4'>
              <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <Label className='text-sm uppercase tracking-[0.2em] text-gray-700'>Name</Label>
                  <Input 
                    placeholder='Your name' 
                    className='bg-white border-[#E6E8EC] focus:ring-red-500 focus:border-red-500'
                    required
                  />
                </div>
                <div className='space-y-2'>
                  <Label className='text-sm uppercase tracking-[0.2em] text-gray-700'>Email</Label>
                  <Input 
                    type='email' 
                    placeholder='<EMAIL>' 
                    className='bg-white border-[#E6E8EC] focus:ring-red-500 focus:border-red-500'
                    required
                  />
                </div>
              </div>
              <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <Label className='text-sm uppercase tracking-[0.2em] text-gray-700'>Phone</Label>
                  <Input 
                    placeholder='+84 ...' 
                    className='bg-white border-[#E6E8EC] focus:ring-red-500 focus:border-red-500'
                    required
                  />
                </div>
                <div className='space-y-2'>
                  <Label className='text-sm uppercase tracking-[0.2em] text-gray-700'>Date</Label>
                  <Input 
                    type='date' 
                    className='bg-white border-[#E6E8EC] focus:ring-red-500 focus:border-red-500'
                  />
                </div>
              </div>
              <div className='space-y-2'>
                <Label className='text-sm uppercase tracking-[0.2em] text-gray-700'>Message</Label>
                <Textarea 
                  rows={5} 
                  placeholder='Tell us about your event...' 
                  className='bg-white border-[#E6E8EC] focus:ring-[#C7B28A] focus:border-[#C7B28A]'
                  required
                />
              </div>
              <Button 
                type='submit' 
                className='w-full bg-black hover:bg-black/90 text-white uppercase tracking-[0.2em] text-xs'
                size='lg'
              >
                Send Message
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

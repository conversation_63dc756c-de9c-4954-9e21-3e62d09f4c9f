import Link from 'next/link';

export function Footer() {
  return (
    <footer className='w-full border-t border-[#E6E8EC] bg-white'>
      <div className='max-w-6xl mx-auto px-6 lg:px-0 py-12 grid grid-cols-1 md:grid-cols-3 gap-8'>
        <div>
          <div className='text-[24px] font-semibold tracking-wide'>H & PA</div>
          <p className='mt-3 text-sm text-gray-600'>
            Elegant weddings and thoughtful events.
          </p>
        </div>
        <nav className='text-sm'>
          <div className='uppercase tracking-[0.3em] text-gray-700 mb-3'>Menu</div>
          <ul className='space-y-2'>
            <li><Link href='/' className='hover:underline text-red-700 hover:text-red-900'>Home</Link></li>
            <li><Link href='/#concept-wedding' className='hover:underline text-red-700 hover:text-red-900'>Concept Wedding</Link></li>
            <li><Link href='/#wedding-dates' className='hover:underline text-red-700 hover:text-red-900'>Wedding Dates</Link></li>
            <li><Link href='/contact' className='hover:underline text-red-700 hover:text-red-900'>Contact</Link></li>
          </ul>
        </nav>
        <div className='text-sm'>
          <div className='uppercase tracking-[0.3em] text-gray-700 mb-3'>Contact</div>
          <ul className='space-y-2 text-gray-700'>
            <li><EMAIL></li>
            <li>+1 (555) 123-4567</li>
            <li>Ho Chi Minh City, VN</li>
          </ul>
        </div>
      </div>
      <div className='border-t border-[#E6E8EC] py-6 text-center text-xs text-gray-500'>
        © {new Date().getFullYear()} H & PA. All rights reserved.
      </div>
    </footer>
  );
}

'use client';

import Link from 'next/link';

export function Header() {
  return (
    <header className='fixed top-0 left-0 right-0 w-full z-50 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70'>
      <div className='h-20 px-6 lg:px-10 flex items-center justify-between border-b border-[#E6E8EC]'>
        <div className='flex items-center'>
          <Link href={'/'} className='text-[28px] leading-none font-semibold tracking-wide select-none'>
            H & PA
          </Link>
        </div>
        <nav className='hidden md:flex items-center gap-8'>
          <Link href={'/'} className='text-xs tracking-[0.3em] uppercase text-red-700 hover:text-red-900 transition-colors'>Home</Link>
          <Link href={'/#concept-wedding'} className='text-xs tracking-[0.3em] uppercase text-red-700 hover:text-red-900 transition-colors'>Concept Wedding</Link>
          <Link href={'/#wedding-dates'} className='text-xs tracking-[0.3em] uppercase text-red-700 hover:text-red-900 transition-colors'>Wedding Dates</Link>
          <Link href={'/contact'} className='text-xs tracking-[0.3em] uppercase text-red-700 hover:text-red-900 transition-colors'>Contact</Link>
        </nav>
      </div>
    </header>
  );
}

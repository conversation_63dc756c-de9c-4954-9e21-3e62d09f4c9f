import fs from 'fs';
import path from 'path';

export interface RSVPData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  attendance: 'yes' | 'no';
  guestCount: string;
  dietaryNotes?: string;
  submittedAt: string;
}

// In-memory storage for production environments
let inMemoryData: RSVPData[] = [];

const dataFilePath = path.join(process.cwd(), 'data', 'rsvp.json');

// Check if we're in a read-only environment
const isReadOnlyEnvironment = () => {
  return process.env.NODE_ENV === 'production' || process.env.VERCEL === '1';
};

// Ensure data directory exists
const ensureDataDir = () => {
  if (isReadOnlyEnvironment()) return;
  
  const dataDir = path.join(process.cwd(), 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
};

// Read RSVP data from file or memory
export const getRSVPData = (): RSVPData[] => {
  try {
    if (isReadOnlyEnvironment()) {
      return inMemoryData;
    }

    ensureDataDir();
    if (!fs.existsSync(dataFilePath)) {
      return [];
    }
    const data = fs.readFileSync(dataFilePath, 'utf8');

    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading RSVP data:', error);

    return isReadOnlyEnvironment() ? inMemoryData : [];
  }
};

// Write RSVP data to file or memory
export const saveRSVPData = (data: RSVPData[]): void => {
  try {
    if (isReadOnlyEnvironment()) {
      inMemoryData = data;

      return;
    }

    ensureDataDir();
    fs.writeFileSync(dataFilePath, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error saving RSVP data:', error);
    if (isReadOnlyEnvironment()) {
      inMemoryData = data;
    } else {
      throw new Error('Failed to save RSVP data');
    }
  }
};

// Add new RSVP entry
export const addRSVPEntry = (rsvpData: Omit<RSVPData, 'id' | 'submittedAt'>): RSVPData => {
  const existingData = getRSVPData();
  const newEntry: RSVPData = {
    ...rsvpData,
    id: Date.now().toString(),
    submittedAt: new Date().toISOString(),
  };
  
  const updatedData = [ ...existingData, newEntry ];
  saveRSVPData(updatedData);

  return newEntry;
};

// Get RSVP statistics
export const getRSVPStats = () => {
  const data = getRSVPData();
  const attending = data.filter(entry => entry.attendance === 'yes');
  const notAttending = data.filter(entry => entry.attendance === 'no');
  const totalGuests = attending.reduce((sum, entry) => {
    const count = parseInt(entry.guestCount) || 1;

    return sum + count;
  }, 0);

  return {
    totalResponses: data.length,
    attending: attending.length,
    notAttending: notAttending.length,
    totalGuests,
  };
};
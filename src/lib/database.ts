import fs from 'fs';
import path from 'path';

export interface RSVPData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  attendance: 'yes' | 'no';
  guestCount: string;
  dietaryNotes?: string;
  submittedAt: string;
}

// Use a different approach for production
const isProduction = process.env.NODE_ENV === 'production' || process.env.VERCEL === '1';

// For production, we'll use a different storage location
const getDataPath = () => {
  if (isProduction) {
    // In production, use /tmp directory which is writable
    return '/tmp/rsvp.json';
  }

  // In development, use local data directory
  return path.join(process.cwd(), 'data', 'rsvp.json');
};

// In-memory fallback for production
let inMemoryData: RSVPData[] = [];

// Ensure data directory exists (only for development)
const ensureDataDir = () => {
  if (isProduction) return;
  
  const dataDir = path.join(process.cwd(), 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
};

// Read RSVP data
export const getRSVPData = (): RSVPData[] => {
  try {
    const dataPath = getDataPath();
    
    if (isProduction) {
      // In production, try to read from /tmp, fallback to memory
      try {
        if (fs.existsSync(dataPath)) {
          const data = fs.readFileSync(dataPath, 'utf8');
          const parsed = JSON.parse(data);
          inMemoryData = parsed; // Keep memory in sync

          return parsed;
        }
      } catch (error) {
        console.error('Could not read from /tmp, using memory storage', error);
      }

      return inMemoryData;
    } else {
      // Development: use local file
      ensureDataDir();
      if (!fs.existsSync(dataPath)) {
        return [];
      }
      const data = fs.readFileSync(dataPath, 'utf8');

      return JSON.parse(data);
    }
  } catch (error) {
    console.error('Error reading RSVP data:', error);

    return isProduction ? inMemoryData : [];
  }
};

// Write RSVP data
export const saveRSVPData = (data: RSVPData[]): void => {
  try {
    const dataPath = getDataPath();
    if (isProduction) {
      // In production, try to write to /tmp, always update memory
      inMemoryData = data;
      try {
        fs.writeFileSync(dataPath, JSON.stringify(data, null, 2));
      } catch (error) {
        console.error('Could not write to /tmp, using memory storage only', error);
      }
    } else {
      // Development: use local file
      ensureDataDir();
      fs.writeFileSync(dataPath, JSON.stringify(data, null, 2));
    }
  } catch (error) {
    console.error('Error saving RSVP data:', error);
    if (isProduction) {
      inMemoryData = data; // Fallback to memory
    } else {
      throw new Error('Failed to save RSVP data');
    }
  }
};

// Add new RSVP entry
export const addRSVPEntry = (rsvpData: Omit<RSVPData, 'id' | 'submittedAt'>): RSVPData => {
  const existingData = getRSVPData();
  const newEntry: RSVPData = {
    ...rsvpData,
    id: Date.now().toString(),
    submittedAt: new Date().toISOString(),
  };
  
  const updatedData = [ ...existingData, newEntry ];
  saveRSVPData(updatedData);

  return newEntry;
};

// Get RSVP statistics
export const getRSVPStats = () => {
  const data = getRSVPData();
  const attending = data.filter(entry => entry.attendance === 'yes');
  const notAttending = data.filter(entry => entry.attendance === 'no');
  const totalGuests = attending.reduce((sum, entry) => {
    const count = parseInt(entry.guestCount) || 1;

    return sum + count;
  }, 0);

  return {
    totalResponses: data.length,
    attending: attending.length,
    notAttending: notAttending.length,
    totalGuests,
  };
};

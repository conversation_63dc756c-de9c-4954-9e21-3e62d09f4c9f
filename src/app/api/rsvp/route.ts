import { NextRequest, NextResponse } from 'next/server';
import { addRSVPEntry, getRSVPStats } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const { firstName, lastName, email, phone, attendance, guestCount, dietaryNotes } = body;
    
    if (!firstName || !lastName || !email || !phone || !attendance || !guestCount) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate attendance value
    if (![ 'yes', 'no' ].includes(attendance)) {
      return NextResponse.json(
        { error: 'Invalid attendance value' },
        { status: 400 }
      );
    }

    // Create RSVP entry
    const rsvpData = {
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      email: email.trim().toLowerCase(),
      phone: phone.trim(),
      attendance,
      guestCount,
      dietaryNotes: dietaryNotes?.trim() || '',
    };

    const newEntry = addRSVPEntry(rsvpData);

    return NextResponse.json(
      { 
        success: true, 
        message: 'RSVP submitted successfully',
        data: newEntry 
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('RSVP submission error:', error);

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const stats = getRSVPStats();

    return NextResponse.json({ stats });
  } catch (error) {
    console.error('Error fetching RSVP stats:', error);

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

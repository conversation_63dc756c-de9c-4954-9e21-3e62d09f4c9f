import { Metadata } from 'next';
import { getRSVPData, getRSVPStats } from '@/lib/database';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

export const metadata: Metadata = {
  title: 'RSVP Admin',
};

export default async function AdminPage() {
  const rsvpData = getRSVPData();
  const stats = getRSVPStats();

  return (
    <div className='min-h-screen bg-[#F5F0EC] pt-24 py-8'>
      <div className='max-w-7xl mx-auto px-6'>
        <div className='mb-8'>
          <h1 className='text-4xl font-serif mb-2'>RSVP Admin Dashboard</h1>
          <div className='h-[2px] w-16 bg-red-500' />
        </div>
        
        {/* Statistics */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
          <Card className='bg-white border-[#E6E8EC]'>
            <CardHeader className='pb-2'>
              <CardTitle className='text-sm font-medium text-gray-600 uppercase tracking-[0.2em]'>Total Responses</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='text-3xl font-bold text-red-500'>{stats.totalResponses}</div>
            </CardContent>
          </Card>
          
          <Card className='bg-white border-[#E6E8EC]'>
            <CardHeader className='pb-2'>
              <CardTitle className='text-sm font-medium text-gray-600 uppercase tracking-[0.2em]'>Attending</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='text-3xl font-bold text-green-600'>{stats.attending}</div>
            </CardContent>
          </Card>
          
          <Card className='bg-white border-[#E6E8EC]'>
            <CardHeader className='pb-2'>
              <CardTitle className='text-sm font-medium text-gray-600 uppercase tracking-[0.2em]'>Not Attending</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='text-3xl font-bold text-red-600'>{stats.notAttending}</div>
            </CardContent>
          </Card>
          
          <Card className='bg-white border-[#E6E8EC]'>
            <CardHeader className='pb-2'>
              <CardTitle className='text-sm font-medium text-gray-600 uppercase tracking-[0.2em]'>Total Guests</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='text-3xl font-bold text-purple-600'>{stats.totalGuests}</div>
            </CardContent>
          </Card>
        </div>

        {/* RSVP List */}
        <Card className='bg-white border-[#E6E8EC]'>
          <CardHeader>
            <CardTitle className='text-2xl font-serif'>RSVP Responses</CardTitle>
          </CardHeader>
          <CardContent>
            {rsvpData.length === 0 ? (
              <div className='text-center py-12'>
                <p className='text-gray-500 text-lg'>No RSVP responses yet.</p>
              </div>
            ) : (
              <div className='overflow-x-auto'>
                <Table>
                  <TableHeader>
                    <TableRow className='border-[#E6E8EC]'>
                      <TableHead className='font-semibold text-gray-700'>Name</TableHead>
                      <TableHead className='font-semibold text-gray-700'>Email</TableHead>
                      <TableHead className='font-semibold text-gray-700'>Phone</TableHead>
                      <TableHead className='font-semibold text-gray-700'>Attendance</TableHead>
                      <TableHead className='font-semibold text-gray-700'>Guests</TableHead>
                      <TableHead className='font-semibold text-gray-700'>Dietary Notes</TableHead>
                      <TableHead className='font-semibold text-gray-700'>Submitted</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {rsvpData.map((rsvp) => (
                      <TableRow key={rsvp.id} className='border-[#E6E8EC] hover:bg-gray-50'>
                        <TableCell className='font-medium'>
                          {rsvp.firstName} {rsvp.lastName}
                        </TableCell>
                        <TableCell className='text-gray-600'>{rsvp.email}</TableCell>
                        <TableCell className='text-gray-600'>{rsvp.phone}</TableCell>
                        <TableCell>
                          <Badge 
                            variant={rsvp.attendance === 'yes' ? 'default' : 'destructive'}
                            className={rsvp.attendance === 'yes' ? 'bg-green-100 text-green-800 hover:bg-green-100' : ''}
                          >
                            {rsvp.attendance === 'yes' ? 'Attending' : 'Not Attending'}
                          </Badge>
                        </TableCell>
                        <TableCell className='text-gray-600'>{rsvp.guestCount}</TableCell>
                        <TableCell className='text-gray-600 max-w-xs truncate'>
                          {rsvp.dietaryNotes || '-'}
                        </TableCell>
                        <TableCell className='text-gray-600'>
                          {new Date(rsvp.submittedAt).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
